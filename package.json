{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@laravel/vite-plugin-wayfinder": "^0.1.3", "@tailwindcss/vite": "^4.1.11", "@types/node": "^22.13.5", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.3.0", "concurrently": "^9.0.1", "eslint": "^9.17.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.2.2", "typescript-eslint": "^8.23.0", "vite": "^7.0.4", "vue-tsc": "^2.2.4"}, "dependencies": {"@inertiajs/vue3": "^2.1.0", "@vueuse/core": "^12.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "laravel-vite-plugin": "^2.0.0", "lucide-vue-next": "^0.468.0", "reka-ui": "^2.4.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.1", "tw-animate-css": "^1.2.5", "vue": "^3.5.13"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.1"}}