<?php
echo "Testing socket creation...\n";

// Test socket creation
$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
if ($socket === false) {
    echo "Failed to create socket: " . socket_strerror(socket_last_error()) . "\n";
    exit(1);
}

echo "Socket created successfully\n";

// Test binding to a port
$result = socket_bind($socket, '127.0.0.1', 8080);
if ($result === false) {
    echo "Failed to bind to port 8080: " . socket_strerror(socket_last_error($socket)) . "\n";
} else {
    echo "Successfully bound to port 8080\n";
}

socket_close($socket);

// Test with different IP
$socket2 = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
$result2 = socket_bind($socket2, '0.0.0.0', 8081);
if ($result2 === false) {
    echo "Failed to bind to 0.0.0.0:8081: " . socket_strerror(socket_last_error($socket2)) . "\n";
} else {
    echo "Successfully bound to 0.0.0.0:8081\n";
}

socket_close($socket2);
?>
