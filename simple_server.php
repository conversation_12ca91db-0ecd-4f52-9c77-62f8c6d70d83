<?php
// Simple HTTP server for Laravel
$host = '127.0.0.1';
$port = 8080;

// Create socket
$socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
if (!$socket) {
    die("Could not create socket\n");
}

// Set socket options
socket_set_option($socket, SOL_SOCKET, SO_REUSEADDR, 1);

// Bind socket
if (!socket_bind($socket, $host, $port)) {
    die("Could not bind to $host:$port\n");
}

// Listen for connections
if (!socket_listen($socket, 5)) {
    die("Could not listen on socket\n");
}

echo "Server running on http://$host:$port\n";
echo "Press Ctrl+C to stop\n";

while (true) {
    $client = socket_accept($socket);
    if ($client) {
        $request = socket_read($client, 1024);
        
        // Simple HTTP response
        $response = "HTTP/1.1 200 OK\r\n";
        $response .= "Content-Type: text/html\r\n";
        $response .= "Connection: close\r\n\r\n";
        $response .= "<h1>Laravel Server Test</h1>";
        $response .= "<p>Server is working on port $port</p>";
        $response .= "<p>Time: " . date('Y-m-d H:i:s') . "</p>";
        
        socket_write($client, $response);
        socket_close($client);
    }
}

socket_close($socket);
?>
